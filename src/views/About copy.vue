<template>
  <div class="about-page">
    <!-- 页面标题区域 -->
    <section class="hero-section" ref="heroSection">
      <div class="container">
        <h1 class="page-title">{{ $t("about.title") }}</h1>
      </div>
    </section>

    <!-- 公司介绍区域 -->
    <section class="company-intro-section" ref="companySection">
      <div class="intro-content">
        <!-- 公司信息 -->
        <div class="company-info">
          <div class="company-header">
            <h2 class="company-name">{{ $t("about.companyName") }}</h2>
            <div class="divider-line"></div>
          </div>

          <div class="company-description">
            <p class="intro-text">{{ $t("about.companyIntro") }}</p>
            <p class="description-text">
              {{ $t("about.companyDescription") }}
            </p>
            <div class="mission-text">
              <p class="text">{{ $t("about.companyMission") }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 品牌定位区域 -->
    <section class="brand-positioning-section" ref="brandSection">
      <div class="container">
        <div class="positioning-content">
          <!-- 圆形装饰图案 -->
          <div class="circular-decoration">
            <div class="circle-group">
              <div class="circle" v-for="i in 40" :key="i"></div>
            </div>
            <div class="brand-logo-overlay">
              <h3 class="brand-title">{{ $t("about.companyName") }}</h3>
            </div>
          </div>

          <!-- 品牌定位内容 -->
          <div class="brand-content">
            <h2 class="section-title">{{ $t("about.brandPositioning") }}</h2>

            <!-- 品牌价值观卡片 -->
            <div class="brand-values">
              <div
                class="value-card"
                v-for="key in Object.keys(brandValues)"
                :key="key"
              >
                <div class="value-icon">
                  <div class="icon-circle"></div>
                </div>
                <h4 class="value-title">
                  {{ $t(`about.brandValues.${key}`) }}
                </h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们区域 -->
    <section class="contact-section" ref="contactSection">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2 class="section-title">{{ $t("about.contact.title") }}</h2>

            <!-- 联系方式列表 -->
            <div class="contact-list">
              <div class="contact-item">
                <div class="contact-icon">
                  <svg width="30" height="30" viewBox="0 0 30 30">
                    <path
                      d="M15 2C10.03 2 6 6.03 6 11c0 7.5 9 15 9 15s9-7.5 9-15c0-4.97-4.03-9-9-9zm0 12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span class="contact-label">{{
                  $t("about.contact.address")
                }}</span>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <svg width="30" height="30" viewBox="0 0 30 30">
                    <path
                      d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span class="contact-label">{{
                  $t("about.contact.phone")
                }}</span>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <svg width="30" height="30" viewBox="0 0 30 30">
                    <path
                      d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span class="contact-label">{{
                  $t("about.contact.email")
                }}</span>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <svg width="30" height="30" viewBox="0 0 30 30">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span class="contact-label">{{
                  $t("about.contact.website")
                }}</span>
              </div>
            </div>
          </div>

          <!-- 装饰性logo -->
          <div class="contact-decoration">
            <div class="logo-decoration">
              <!-- 这里可以放置公司logo的装饰性展示 -->
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import Navigation from "@/components/Navigation.vue";
import Footer from "@/components/Footer.vue";

// 响应式引用
const heroSection = ref(null);
const companySection = ref(null);
const brandSection = ref(null);
const contactSection = ref(null);

// 品牌价值观数据
const brandValues = ref({
  innovation: "innovation",
  professional: "professional",
  integrity: "integrity",
  collaboration: "collaboration",
});

// 滚动监听器
let scrollListener = null;

// 处理滚动事件，实现背景图片逐渐显示效果
function handleScroll() {
  const scrollY = window.scrollY;
  const windowHeight = window.innerHeight;

  // 获取各个section的位置
  const sections = [
    { element: heroSection.value, bgClass: "hero-bg" },
    { element: companySection.value, bgClass: "company-bg" },
    { element: brandSection.value, bgClass: "brand-bg" },
    { element: contactSection.value, bgClass: "contact-bg" },
  ];

  sections.forEach((section) => {
    if (section.element) {
      const rect = section.element.getBoundingClientRect();
      const sectionTop = scrollY + rect.top;
      const sectionHeight = rect.height;

      // 计算section在视窗中的可见程度
      const visibleStart = Math.max(0, scrollY - sectionTop);
      const visibleEnd = Math.min(
        sectionHeight,
        scrollY + windowHeight - sectionTop
      );
      const visibleHeight = Math.max(0, visibleEnd - visibleStart);
      const visibilityRatio = visibleHeight / windowHeight;

      // 根据可见程度调整背景透明度
      const opacity = Math.min(1, visibilityRatio * 1.5);
      section.element.style.setProperty("--bg-opacity", opacity);
    }
  });
}

// 生命周期钩子
onMounted(() => {
  // 添加滚动监听器
  scrollListener = () => {
    requestAnimationFrame(handleScroll);
  };
  window.addEventListener("scroll", scrollListener, { passive: true });

  // 初始化背景显示
  handleScroll();
});

onUnmounted(() => {
  // 清理滚动监听器
  if (scrollListener) {
    window.removeEventListener("scroll", scrollListener);
  }
});
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  overflow-x: hidden;
}

// 页面标题区域
.hero-section {
  @include flex-center;
  .container {
    margin-bottom: 60px;
  }
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 70px;
  }

  @include tablet-only {
    font-size: 50px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}

// 公司介绍区域
.company-intro-section {
  position: relative;
  padding: 120px 0;
  background-color: #181818;

  .container {
    position: relative;
    z-index: 2;
  }
}

.intro-content {
  min-height: 780px;
  background-image: url("../assets/imgs/about-bg-wave.png");
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;

  @include tablet-only {
    @include flex-column;
    gap: 80px;
  }

  @include mobile-only {
    height: auto;
    background-size: cover;
    @include flex-column;
    gap: 60px;
  }
}

// 公司信息
.company-info {
  flex: 1;
  max-width: 1260px;
  margin: 0 auto;
}

.company-header {
  margin-bottom: 60px;

  @media (max-width: 1280px) {
    width: 940px;
    margin: 0 auto 60px;
  }
  @media (max-width: 768px) {
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    margin-bottom: 40px;
  }
}

.company-name {
  @include font-chinese($font-size-4xl, $font-weight-bold);
  color: $text-white;
  margin: 0 0 20px 0;

  @include tablet-only {
    font-size: $font-size-3xl;
  }

  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.divider-line {
  width: 30px;
  height: 2px;
  background: $text-white;
}

.company-description {
  width: 940px;
  margin: 0 auto;
  @include flex-column;
  gap: 30px;

  @media (max-width: 768px) {
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    gap: 20px;
  }
}

.intro-text {
  @include font-chinese($font-size-2xl, $font-weight-light);
  color: #c5c5c5;
  line-height: 1.8;

  @media (max-width: 768px) {
    font-size: $font-size-2xl;
  }

  @include mobile-only {
    width: 100%;
    font-size: $font-size-lg;
  }
}

.description-text {
  @include font-chinese($font-size-4xl, $font-weight-light);
  color: $text-white;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: $font-size-4xl;
  }
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.mission-text {
  display: flex;
  justify-content: flex-end;
  margin: 120px 30px 0 0;

  .text {
    width: 426px;
    @include font-chinese($font-size-2xl, $font-weight-light);
    color: #c5c5c5;
    line-height: 2;

    @include mobile-only {
      width: 100%;
      font-size: $font-size-sm;
    }
  }
  @include mobile-only {
    margin-top: 60px;
  }
}

// 品牌定位区域
.brand-positioning-section {
  position: relative;
  min-height: 100vh;
  @include flex-center;
  background-image: url("@/assets/imgs/about-bg-4.png");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;

  @media (min-width: 1440px) {
    background-size: cover;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    opacity: var(--bg-opacity, 0);
    @include transition(opacity, 0.3s, ease);
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.positioning-content {
  @include flex-between;
  align-items: center;
  gap: 120px;

  @include tablet-only {
    @include flex-column;
    gap: 80px;
  }

  @include mobile-only {
    @include flex-column;
    gap: 60px;
  }
}

// 圆形装饰图案
.circular-decoration {
  position: relative;
  width: 500px;
  height: 500px;
  flex-shrink: 0;

  @include tablet-only {
    width: 400px;
    height: 400px;
  }

  @include mobile-only {
    width: 300px;
    height: 300px;
  }
}

.circle-group {
  position: relative;
  width: 100%;
  height: 100%;
}

.circle {
  position: absolute;
  border: 1px solid $text-light;
  border-radius: 50%;
  opacity: 0.3;

  // 随机分布不同大小的圆圈
  &:nth-child(1) {
    width: 20px;
    height: 20px;
    top: 10%;
    left: 20%;
  }
  &:nth-child(2) {
    width: 30px;
    height: 30px;
    top: 15%;
    left: 60%;
  }
  &:nth-child(3) {
    width: 25px;
    height: 25px;
    top: 25%;
    left: 80%;
  }
  &:nth-child(4) {
    width: 35px;
    height: 35px;
    top: 40%;
    left: 15%;
  }
  &:nth-child(5) {
    width: 28px;
    height: 28px;
    top: 45%;
    left: 70%;
  }
  &:nth-child(6) {
    width: 22px;
    height: 22px;
    top: 60%;
    left: 30%;
  }
  &:nth-child(7) {
    width: 32px;
    height: 32px;
    top: 65%;
    left: 85%;
  }
  &:nth-child(8) {
    width: 26px;
    height: 26px;
    top: 80%;
    left: 10%;
  }
  &:nth-child(9) {
    width: 24px;
    height: 24px;
    top: 85%;
    left: 50%;
  }
  &:nth-child(10) {
    width: 29px;
    height: 29px;
    top: 30%;
    left: 40%;
  }
  // 继续添加更多圆圈...
  &:nth-child(n + 11) {
    width: 20px;
    height: 20px;
    opacity: 0.2;
  }
}

.brand-logo-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  @include flex-center;
  width: 200px;
  height: 200px;
  border: 2px solid $text-primary;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);

  @include mobile-only {
    width: 150px;
    height: 150px;
  }
}

.brand-title {
  @include font-chinese(36px, $font-weight-bold);
  color: $text-primary;
  margin: 0;
  text-align: center;

  @include mobile-only {
    font-size: 28px;
  }
}

// 品牌内容
.brand-content {
  flex: 1;
  max-width: 600px;
}

.section-title {
  @include font-chinese(48px, $font-weight-bold);
  color: $text-primary;
  margin: 0 0 60px 0;

  @include tablet-only {
    font-size: 40px;
    margin-bottom: 40px;
  }

  @include mobile-only {
    font-size: 32px;
    margin-bottom: 30px;
  }
}

// 品牌价值观卡片
.brand-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.value-card {
  @include flex-column;
  align-items: center;
  gap: 20px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  @include transition;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
  }

  @include mobile-only {
    padding: 20px;
  }
}

.value-icon {
  @include flex-center;
  width: 60px;
  height: 60px;

  @include mobile-only {
    width: 50px;
    height: 50px;
  }
}

.icon-circle {
  width: 100%;
  height: 100%;
  border: 2px solid $text-primary;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.value-title {
  @include font-chinese($font-size-lg, $font-weight-normal);
  color: $text-primary;
  margin: 0;
  text-align: center;

  @include mobile-only {
    font-size: $font-size-base;
  }
}

// 联系我们区域
.contact-section {
  position: relative;
  min-height: 100vh;
  @include flex-center;
  background-image: url("@/assets/imgs/about-bg-5.png"),
    url("@/assets/imgs/about-bg-6.png");
  background-size: cover, cover;
  background-position: center, center;
  background-attachment: fixed, fixed;

  @media (min-width: 1440px) {
    background-size: cover, cover;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: var(--bg-opacity, 0);
    @include transition(opacity, 0.3s, ease);
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.contact-content {
  @include flex-between;
  align-items: flex-start;
  gap: 120px;

  @include tablet-only {
    @include flex-column;
    gap: 80px;
  }

  @include mobile-only {
    @include flex-column;
    gap: 60px;
  }
}

.contact-info {
  flex: 1;
  max-width: 600px;
}

// 联系方式列表
.contact-list {
  @include flex-column;
  gap: 40px;

  @include mobile-only {
    gap: 30px;
  }
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  @include transition;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(10px);
  }

  @include mobile-only {
    padding: 15px;
    gap: 15px;
  }
}

.contact-icon {
  @include flex-center;
  width: 50px;
  height: 50px;
  color: $text-primary;
  flex-shrink: 0;

  @include mobile-only {
    width: 40px;
    height: 40px;
  }

  svg {
    width: 100%;
    height: 100%;
  }
}

.contact-label {
  @include font-chinese($font-size-lg, $font-weight-normal);
  color: $text-primary;

  @include mobile-only {
    font-size: $font-size-base;
  }
}

// 装饰性logo区域
.contact-decoration {
  flex-shrink: 0;
  width: 400px;
  height: 400px;
  @include flex-center;

  @include tablet-only {
    width: 300px;
    height: 300px;
  }

  @include mobile-only {
    width: 200px;
    height: 200px;
  }
}

.logo-decoration {
  width: 100%;
  height: 100%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  position: relative;

  // 添加一些装饰性元素
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
  }

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    height: 30%;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
  }
}

// 响应式调整
@include mobile-only {
  .company-intro-section,
  .brand-positioning-section,
  .contact-section {
    height: auto;
    padding: 40px 20px;
  }

  .page-title {
    font-size: 48px;
  }
}
</style>
