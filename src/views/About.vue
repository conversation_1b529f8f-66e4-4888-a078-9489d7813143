<template>
  <div class="about-page">
    <!-- 页面标题区域 -->
    <section class="hero-section" ref="heroSection">
      <div class="container">
        <h1 class="page-title">{{ $t("about.title") }}</h1>
      </div>
    </section>

    <!-- 公司介绍区域 -->
    <section class="company-intro-section" ref="companySection">
      <div class="intro-content">
        <!-- 公司信息 -->
        <div class="company-info">
          <div class="company-header">
            <h2 class="company-name">{{ $t("about.companyName") }}</h2>
            <div class="divider-line"></div>
          </div>

          <div class="company-description">
            <p class="intro-text">{{ $t("about.companyIntro") }}</p>
            <p class="description-text">
              {{ $t("about.companyDescription") }}
            </p>
            <div class="mission-text">
              <p class="text">{{ $t("about.companyMission") }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  overflow-x: hidden;
}

// 页面标题区域
.hero-section {
  @include flex-center;
  .container {
    margin-bottom: 60px;
  }
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 70px;
  }

  @include tablet-only {
    font-size: 50px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}

// 公司介绍区域
.company-intro-section {
  position: relative;
  padding: 120px 0;
  background-color: #181818;

  .container {
    position: relative;
    z-index: 2;
  }
}

.intro-content {
  min-height: 780px;
  background-image: url("../assets/imgs/about-bg-wave.png");
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;

  @include tablet-only {
    @include flex-column;
    gap: 80px;
  }

  @include mobile-only {
    height: auto;
    background-size: cover;
    @include flex-column;
    gap: 60px;
  }
}

// 公司信息
.company-info {
  flex: 1;
  max-width: 1260px;
  margin: 0 auto;
}

.company-header {
  margin-bottom: 60px;

  @media (max-width: 1280px) {
    width: 940px;
    margin: 0 auto 60px;
  }
  @media (max-width: 768px) {
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    margin-bottom: 40px;
  }
}

.company-name {
  @include font-chinese($font-size-4xl, $font-weight-bold);
  color: $text-white;
  margin: 0 0 20px 0;

  @include tablet-only {
    font-size: $font-size-3xl;
  }

  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.divider-line {
  width: 30px;
  height: 2px;
  background: $text-white;
}

.company-description {
  width: 940px;
  margin: 0 auto;
  @include flex-column;
  gap: 30px;

  @media (max-width: 768px) {
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    gap: 20px;
  }
}

.intro-text {
  @include font-chinese($font-size-2xl, $font-weight-light);
  color: #c5c5c5;
  line-height: 1.8;

  @media (max-width: 768px) {
    font-size: $font-size-2xl;
  }

  @include mobile-only {
    width: 100%;
    font-size: $font-size-lg;
  }
}

.description-text {
  @include font-chinese($font-size-4xl, $font-weight-light);
  color: $text-white;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: $font-size-4xl;
  }
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.mission-text {
  display: flex;
  justify-content: flex-end;
  margin: 120px 30px 0 0;

  .text {
    width: 426px;
    @include font-chinese($font-size-2xl, $font-weight-light);
    color: #c5c5c5;
    line-height: 2;

    @include mobile-only {
      width: 100%;
      font-size: $font-size-sm;
    }
  }
  @include mobile-only {
    margin-top: 60px;
  }
}

// 品牌定位区域

// 响应式调整
@include mobile-only {
  .company-intro-section,
  .brand-positioning-section,
  .contact-section {
    height: auto;
    padding: 40px 20px;
  }

  .page-title {
    font-size: 48px;
  }
}
</style>
