<template>
  <div class="about-page">
    <!-- 页面标题区域 -->
    <section class="hero-section" ref="heroSection">
      <div class="container">
        <h1 class="page-title">{{ $t("about.title") }}</h1>
      </div>
    </section>

    <!-- 公司介绍区域 -->
    <section class="company-intro-section" ref="companySection">
      <div
        class="intro-content"
        data-aos="zoom-in"
        data-aos-delay="0"
        data-aos-duration="1000"
      >
        <!-- 公司信息 -->
        <div class="company-info">
          <div class="company-header" data-aos="fade-right">
            <h2 class="company-name">{{ $t("about.companyName") }}</h2>
            <div class="divider-line"></div>
          </div>

          <div class="company-description">
            <div
              data-aos="fade-zoom-in"
              data-aos-delay="1000"
              data-aos-offset="0"
            >
              <p class="intro-text">{{ $t("about.companyIntro") }}</p>
              <p class="description-text">
                {{ $t("about.companyDescription") }}
              </p>
            </div>
            <div class="mission-text" data-aos="fade-left">
              <p class="text">{{ $t("about.companyMission") }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 品牌定位区域 -->
    <section class="brand-positioning-section">
      <div class="positioning-content">
        <h2 class="section-title" data-aos="fade-up" data-aos-duration="1000">
          {{ $t("about.brandPositioning") }}
        </h2>
        <!-- 品牌定位内容 -->
        <div class="brand-content">
          <div
            class="brand-values bg1"
            data-aos="fade-up"
            data-aos-duration="2000"
          >
            <p class="brand-text">
              我们连接不同地域文化、声音身份与表达方式，创作兼具人文温度与国际视野的当代表达。
            </p>
          </div>
          <div
            class="brand-values bg2"
            data-aos="fade-up"
            data-aos-duration="2000"
            data-aos-delay="100"
          >
            <p class="brand-text white">
              海之轨迹是一个以“文化交汇"和"声音创新"为核心理念的音乐厂牌。
            </p>
          </div>
          <div
            class="brand-values bg3"
            data-aos="fade-up"
            data-aos-duration="2000"
            data-aos-delay="200"
          >
            <p class="brand-text">
              在这里，音乐不仅是旋律，更是身份、记忆和时代的共鸣场。
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="slogan-section">
      <div class="slogan-bg">
        <div
          class="slogan-text"
          data-aos="fade-zoom-in"
          data-aos-easing="ease-in"
          data-aos-duration="1000"
        >
          <div class="icons-top"></div>
          <p>让每段创作轨迹<br />都被世界听见</p>
          <div class="icons-bottom"></div>
        </div>
      </div>
    </section>
    <section class="contact-section">
      <div class="contact-content">
        <div class="contact-info">
          <div
            class="contact-label"
            data-aos="fade-up"
            data-aos-anchor-placement="center-bottom"
          >
            <p>{{ $t("about.contact.phone") }}</p>
            <p class="p1">17771774074</p>
          </div>
          <div
            class="contact-label"
            data-aos="fade-up"
            data-aos-anchor-placement="center-bottom"
          >
            <p>{{ $t("about.contact.email") }}</p>
            <p class="p1"><EMAIL></p>
          </div>
        </div>
        <h2
          class="section-title-contact"
          data-aos="fade-zoom-in"
          data-aos-delay="1000"
          data-aos-offset="0"
        >
          {{ $t("about.contact.title") }}
        </h2>
        <div
          class="contact-label"
          data-aos="fade-up"
          data-aos-anchor-placement="center-bottom"
        >
          <p>{{ $t("about.contact.address") }}</p>
          <p class="p1">湖北省武汉市硚口区长生路5号</p>
        </div>
      </div>
      <div class="contact-img">
        <img src="../assets/imgs/about-bg-contact.png" alt="contact" />
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  overflow-x: hidden;
}

// 页面标题区域
.hero-section {
  @include flex-center;
  .container {
    margin-bottom: 60px;
  }
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 70px;
  }

  @include tablet-only {
    font-size: 50px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}

// 公司介绍区域
.company-intro-section {
  position: relative;
  padding: 120px 0;
  background-color: #181818;

  .container {
    position: relative;
    z-index: 2;
  }
}

.intro-content {
  min-height: 780px;
  background-image: url("../assets/imgs/about-bg-wave.png");
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;

  @include tablet-only {
    @include flex-column;
    gap: 80px;
  }

  @include mobile-only {
    min-height: 550px;
    background-size: cover;
    @include flex-column;
    gap: 60px;
  }
}

// 公司信息
.company-info {
  flex: 1;
  max-width: 1260px;
  margin: 0 auto;
}

.company-header {
  padding-top: 60px;
  margin-bottom: 80px;

  @media (max-width: 1280px) {
    width: 940px;
    margin: 0 auto 60px;
  }
  @media (max-width: 768px) {
    padding-top: 10px;
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    margin-bottom: 40px;
  }
}

.company-name {
  @include font-chinese($font-size-4xl, $font-weight-bold);
  color: $text-white;
  margin: 0 0 60px 0;

  @include tablet-only {
    font-size: $font-size-3xl;
  }

  @include mobile-only {
    font-size: $font-size-3xl;
  }
}

.divider-line {
  width: 30px;
  height: 2px;
  background: $text-white;
}

.company-description {
  width: 940px;
  margin: 0 auto;
  @include flex-column;
  gap: 30px;

  @media (max-width: 768px) {
    width: 680px;
    margin: 0 auto 60px;
  }
  @include mobile-only {
    width: 100%;
    gap: 20px;
  }
}

.intro-text {
  @include font-chinese($font-size-2xl, $font-weight-light);
  color: #c5c5c5;
  line-height: 2em;

  @media (max-width: 768px) {
    font-size: $font-size-2xl;
  }

  @include mobile-only {
    width: 100%;
    font-size: $font-size-lg;
  }
}

.description-text {
  @include font-chinese($font-size-4xl, $font-weight-light);
  color: $text-white;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: $font-size-4xl;
  }
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.mission-text {
  display: flex;
  justify-content: flex-end;
  margin: 120px 30px 0 0;

  .text {
    width: 426px;
    @include font-chinese($font-size-2xl, $font-weight-light);
    color: #c5c5c5;
    line-height: 2;

    @include mobile-only {
      width: 100%;
      font-size: $font-size-sm;
    }
  }
  @include mobile-only {
    margin-top: 60px;
  }
}

// 品牌定位区域
.brand-positioning-section {
  height: 1465px;
  padding-top: 120px;
  background: url("../assets/imgs/about-bg-loop.png") no-repeat center/95%;
}
.section-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin-left: 120px;

  @include desktop-only {
    font-size: 70px;
  }

  @include tablet-only {
    font-size: 50px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}
.brand-content {
  width: 1440px;
  height: 600px;
  margin: 0 auto;
  position: relative;

  @include mobile-only {
    width: 100%;
  }
}
.brand-values {
  width: 344px;
  height: 390px;
  position: absolute;

  &.bg1 {
    width: 344px;
    height: 430px;
    top: 360px;
    left: 180px;
    background: url("../assets/imgs/value-bg1.png") no-repeat center/cover;
  }
  &.bg2,
  &.bg3 {
    width: 344px;
    height: 390px;
  }

  &.bg2 {
    top: 60px;
    right: 60px;
    background: url("../assets/imgs/value-bg2.png") no-repeat center/cover;
  }

  &.bg3 {
    top: 720px;
    left: 780px;
    background: url("../assets/imgs/value-bg3.png") no-repeat center/cover;
  }

  .brand-text {
    width: 240px;
    margin: 20px 40px 0 64px;
    @include font-chinese($font-size-2xl, $font-weight-light);
    font-size: 20px;
    line-height: 2em;
  }
}
.white {
  color: white;
}
@media (max-width: 1280px) {
  .brand-positioning-section {
    height: 1465px;
    padding-top: 100px;
  }
  .section-title {
    font-size: 70px;
  }
  .brand-content {
    width: 1200px;
    height: 600px;
    margin: 0 auto;
    position: relative;
  }
  .brand-values {
    width: 344px;
    height: 390px;
    position: absolute;

    &.bg1 {
      left: 80px;
    }

    &.bg3 {
      top: 700px;
      left: 580px;
    }
  }
}
@media (max-width: 1024px) {
  .brand-content {
    width: 1000px;
  }
  .brand-values {
    &.bg2 {
      right: 0px;
    }
  }
}
@media (max-width: 768px) {
  .brand-positioning-section {
    height: auto;
    padding-bottom: 100px;
  }
  .section-title {
    font-size: 70px;
    margin-left: 0;
    padding-bottom: 30px;
    text-align: center;
  }
  .brand-content {
    width: 700px;
    height: auto;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
  .brand-values {
    position: static;
  }
}
@include mobile-only {
  .brand-content {
    width: 100%;
  }
}
//slogan部分
.slogan-section {
  height: 961px;
  background: #000 url("../assets/imgs/about-bg-slogan.png") no-repeat
    center/cover;
  @include flex-center;

  .slogan-bg {
    width: 960px;
    height: 960px;
    background: url("../assets/imgs/slogan-bg.png") no-repeat center/cover;
    @include flex-center;
    justify-content: space-between;
  }

  .slogan-text {
    width: 730px;
    margin: 0 auto;
    position: relative;
  }

  .icons-top {
    width: 40px;
    height: 40px;
    border-top: 3px solid #fff;
    border-left: 3px solid #fff;
    position: absolute;
    top: 0;
    left: 0;
  }

  .icons-bottom {
    width: 40px;
    height: 40px;
    border-right: 3px solid #fff;
    border-bottom: 3px solid #fff;
    position: absolute;
    bottom: 0;
    right: 0;
  }

  p {
    @include font-chinese($font-size-7xl, $font-weight-light);
    font-weight: 250;
    color: $text-white;
    line-height: 1.2;
    text-align: center;
  }
}
@media (max-width: 768px) {
  .slogan-section {
    height: 500px;

    .slogan-bg {
      width: 500px;
      height: 500px;
    }

    .slogan-text {
      width: 500px;
      margin: 0 auto;
      position: relative;
    }
    .icons-top {
      border-top: 2px solid #fff;
      border-left: 2px solid #fff;
    }

    .icons-bottom {
      border-right: 2px solid #fff;
      border-bottom: p2x solid #fff;
    }

    p {
      font-size: 60px;
      font-weight: 250;
    }
  }
}
@include mobile-only {
  .slogan-section {
    height: 300px;

    .slogan-bg {
      width: 300px;
      height: 300px;
    }

    .slogan-text {
      max-width: 300;
      margin: 0 auto;
      position: relative;
    }
    .icons-top {
      width: 20px;
      height: 20px;
      border-top: 1px solid #fff;
      border-left: 1px solid #fff;
    }

    .icons-bottom {
      width: 20px;
      height: 20px;
      border-right: 1px solid #fff;
      border-bottom: 1px solid #fff;
    }

    p {
      font-size: 30px;
      font-weight: 250;
    }
  }
}
.contact-section {
  max-width: 1200px;
  margin: 180px auto;
  @include flex-between;
}
.contact-content {
  height: 840px;
  @include flex-column;
  justify-content: space-between;
  align-content: flex-start;
}
.section-title-contact {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
}
.contact-info {
  display: flex;
}
.contact-label {
  margin-right: 65px;
  p {
    @include font-chinese(20px, $font-weight-bold);
    color: $text-primary;
  }
  .p1 {
    @include font-chinese(26px, $font-weight-light);
  }
}
.contact-img {
  width: 600px;
  height: 840px;

  img {
    @include image-cover;
  }
}

@media (max-width: 1024px) {
  .contact-section {
    max-width: 900px;
    margin: 100px auto;
  }
  .contact-content {
    height: 600px;
  }
  .section-title-contact {
    font-size: 60px;
  }
  .contact-label {
    margin-right: 45px;
    .p1 {
      font-size: 22px;
    }
  }
  .contact-img {
    width: 400px;
    height: 600px;
  }
}
@media (max-width: 768px) {
  .contact-section {
    max-width: 700px;
    margin: 80px auto;
  }
  .section-title-contact {
    font-size: 50px;
  }
  .contact-content {
    height: 400px;
  }
  .contact-label {
    p {
      font-size: 18px;
    }
    .p1 {
      font-size: 20px;
    }
  }
  .contact-img {
    width: 300px;
    height: 400px;
  }
}
@include mobile-only {
  .contact-section {
    width: 340px;
    margin: 40px auto;
    @include flex-column;
  }
  .section-title-contact {
    font-size: 40px;
  }
  .contact-info {
    flex-direction: column;
    gap: 20px;
  }
  .contact-content {
    margin-bottom: 50px;
  }
}

// 响应式调整
@include mobile-only {
  .company-intro-section,
  .brand-positioning-section {
    height: auto;
    padding: 40px 20px;
  }

  .page-title {
    font-size: 48px;
  }
}
</style>
